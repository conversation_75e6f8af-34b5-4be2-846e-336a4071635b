import 'dart:developer';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/supporter_model.dart';

class LocalDatabaseService {
  static const String _supportersBoxName = 'supporters';
  static const String _syncBoxName = 'sync_data';
  
  Box<SupporterModel>? _supportersBox;
  Box? _syncBox;

  // Initialize Hive and open boxes
  Future<void> initialize() async {
    try {
      await Hive.initFlutter();
      
      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(SupporterModelAdapter());
      }

      // Open boxes
      _supportersBox = await Hive.openBox<SupporterModel>(_supportersBoxName);
      _syncBox = await Hive.openBox(_syncBoxName);
      
      log('✅ Local database initialized successfully');
    } catch (e) {
      log('❌ Error initializing local database: $e');
      rethrow;
    }
  }

  // Get all supporters from local database
  List<SupporterModel> getAllSupporters() {
    try {
      if (_supportersBox == null) {
        log('❌ Supporters box is not initialized');
        return [];
      }
      
      final supporters = _supportersBox!.values.toList();
      log('📱 Retrieved ${supporters.length} supporters from local database');
      return supporters;
    } catch (e) {
      log('❌ Error getting supporters: $e');
      return [];
    }
  }

  // Save supporters to local database
  Future<bool> saveSupporters(List<SupporterModel> supporters) async {
    try {
      if (_supportersBox == null) {
        log('❌ Supporters box is not initialized');
        return false;
      }

      // Clear existing data
      await _supportersBox!.clear();
      
      // Add new supporters
      for (int i = 0; i < supporters.length; i++) {
        await _supportersBox!.put(i, supporters[i]);
      }
      
      // Update last sync time
      await _syncBox?.put('last_sync', DateTime.now().toIso8601String());
      
      log('✅ Saved ${supporters.length} supporters to local database');
      return true;
    } catch (e) {
      log('❌ Error saving supporters: $e');
      return false;
    }
  }

  // Get last sync time
  DateTime? getLastSyncTime() {
    try {
      final lastSyncString = _syncBox?.get('last_sync');
      if (lastSyncString != null) {
        return DateTime.parse(lastSyncString);
      }
      return null;
    } catch (e) {
      log('❌ Error getting last sync time: $e');
      return null;
    }
  }

  // Check if sync is needed (48 hours)
  bool isSyncNeeded() {
    final lastSync = getLastSyncTime();
    if (lastSync == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    // Check if 48 hours have passed
    return difference.inHours >= 48;
  }

  // Compare two lists of supporters
  bool areSupporersEqual(List<SupporterModel> list1, List<SupporterModel> list2) {
    if (list1.length != list2.length) return false;
    
    // Sort both lists by phone number for comparison
    list1.sort((a, b) => a.phone.compareTo(b.phone));
    list2.sort((a, b) => a.phone.compareTo(b.phone));
    
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    
    return true;
  }

  // Get supporter count
  int getSupporterCount() {
    return _supportersBox?.length ?? 0;
  }

  // Close boxes
  Future<void> close() async {
    await _supportersBox?.close();
    await _syncBox?.close();
  }

  // Clear all data (for testing purposes)
  Future<void> clearAllData() async {
    await _supportersBox?.clear();
    await _syncBox?.clear();
    log('🗑️ Cleared all local database data');
  }
}
