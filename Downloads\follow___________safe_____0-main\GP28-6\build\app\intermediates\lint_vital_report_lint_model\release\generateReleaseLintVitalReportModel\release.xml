<variant
    name="release"
    package="com.example.pro"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.3.2;C:\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\tmp\kotlin-classes\release;C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\kotlinToolingMetadata;C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.pro"
      generatedSourceFolders="C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\generated\ap_generated_sources\release\out;C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\generated\source\buildConfig\release"
      generatedResourceFolders="C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\7da497cdaea7c2943cd33805f3efda87\transformed\desugar_jdk_libs_configuration-2.1.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\transforms-3\ffc48483c2394da92fed0c3f42815df4\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
