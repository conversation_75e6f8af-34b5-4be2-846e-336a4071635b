import 'dart:async';
import 'dart:developer';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isOnline = false;
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();

  // Stream to listen to connectivity changes
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  // Current connectivity status
  bool get isOnline => _isOnline;

  // Initialize connectivity service
  Future<void> initialize() async {
    try {
      // Check initial connectivity status
      await _updateConnectionStatus();
      
      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        (List<ConnectivityResult> results) {
          _handleConnectivityChange(results);
        },
      );
      
      log('✅ Connectivity service initialized successfully');
    } catch (e) {
      log('❌ Error initializing connectivity service: $e');
    }
  }

  // Handle connectivity changes
  void _handleConnectivityChange(List<ConnectivityResult> results) {
    _updateConnectionStatus();
  }

  // Update connection status
  Future<void> _updateConnectionStatus() async {
    try {
      final List<ConnectivityResult> connectivityResults = await _connectivity.checkConnectivity();
      
      bool hasConnection = false;
      
      for (ConnectivityResult result in connectivityResults) {
        if (result == ConnectivityResult.mobile || 
            result == ConnectivityResult.wifi || 
            result == ConnectivityResult.ethernet) {
          hasConnection = true;
          break;
        }
      }

      // Update status if changed
      if (_isOnline != hasConnection) {
        _isOnline = hasConnection;
        _connectionStatusController.add(_isOnline);
        
        log('🌐 Connectivity status changed: ${_isOnline ? "ONLINE" : "OFFLINE"}');
      }
    } catch (e) {
      log('❌ Error checking connectivity: $e');
      // Assume offline on error
      if (_isOnline) {
        _isOnline = false;
        _connectionStatusController.add(_isOnline);
      }
    }
  }

  // Check if device has internet connection
  Future<bool> hasInternetConnection() async {
    try {
      final List<ConnectivityResult> connectivityResults = await _connectivity.checkConnectivity();
      
      for (ConnectivityResult result in connectivityResults) {
        if (result == ConnectivityResult.mobile || 
            result == ConnectivityResult.wifi || 
            result == ConnectivityResult.ethernet) {
          return true;
        }
      }
      
      return false;
    } catch (e) {
      log('❌ Error checking internet connection: $e');
      return false;
    }
  }

  // Get current connectivity type
  Future<String> getConnectivityType() async {
    try {
      final List<ConnectivityResult> connectivityResults = await _connectivity.checkConnectivity();
      
      if (connectivityResults.isEmpty) {
        return 'none';
      }
      
      // Return the first available connection type
      final result = connectivityResults.first;
      
      switch (result) {
        case ConnectivityResult.wifi:
          return 'wifi';
        case ConnectivityResult.mobile:
          return 'mobile';
        case ConnectivityResult.ethernet:
          return 'ethernet';
        case ConnectivityResult.bluetooth:
          return 'bluetooth';
        case ConnectivityResult.vpn:
          return 'vpn';
        case ConnectivityResult.other:
          return 'other';
        case ConnectivityResult.none:
        default:
          return 'none';
      }
    } catch (e) {
      log('❌ Error getting connectivity type: $e');
      return 'none';
    }
  }

  // Wait for internet connection (with timeout)
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 10)}) async {
    if (_isOnline) return true;
    
    try {
      return await _connectionStatusController.stream
          .where((isConnected) => isConnected)
          .timeout(timeout)
          .first
          .then((_) => true);
    } catch (e) {
      log('⏰ Timeout waiting for internet connection');
      return false;
    }
  }

  // Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectionStatusController.close();
  }
}
