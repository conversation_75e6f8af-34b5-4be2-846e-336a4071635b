{"logs": [{"outputFile": "com.example.pro.app-mergeDebugResources-43:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ae188e1632e2151ea3440bd966c549e8\\transformed\\preference-1.2.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,262,341,488,657,737", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "172,257,336,483,652,732,810"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5765,5942,6345,6424,6751,6920,7000", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "5832,6022,6419,6566,6915,6995,7073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13ca1886765b845003c294694287a150\\transformed\\browser-1.8.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5837,6027,6128,6242", "endColumns": "104,100,113,102", "endOffsets": "5937,6123,6237,6340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3ad2769cfe3f8f14a92393f5169ebde4\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4511", "endColumns": "150", "endOffsets": "4657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\713c4a2f784e4f0f5a173a73d6f96c0d\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2767,2865,2967,3070,3171,3273,3371,6650", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "2860,2962,3065,3166,3268,3366,3495,6746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8daa030cc929207e77a9120361871ed1\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3500,3607,3780,3910,4019,4166,4295,4408,4662,4824,4933,5106,5238,5391,5552,5617,5683", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "3602,3775,3905,4014,4161,4290,4403,4506,4819,4928,5101,5233,5386,5547,5612,5678,5760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\581188cde9253f2ea651785e94e07b23\\transformed\\appcompat-1.1.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,865,956,1048,1142,1236,1337,1430,1525,1619,1710,1801,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,77,90,91,93,93,100,92,94,93,90,90,77,109,102,95,110,101,109,158,96,78", "endOffsets": "203,300,405,491,591,704,782,860,951,1043,1137,1231,1332,1425,1520,1614,1705,1796,1874,1984,2087,2183,2294,2396,2506,2665,2762,2841"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,865,956,1048,1142,1236,1337,1430,1525,1619,1710,1801,1879,1989,2092,2188,2299,2401,2511,2670,6571", "endColumns": "102,96,104,85,99,112,77,77,90,91,93,93,100,92,94,93,90,90,77,109,102,95,110,101,109,158,96,78", "endOffsets": "203,300,405,491,591,704,782,860,951,1043,1137,1231,1332,1425,1520,1614,1705,1796,1874,1984,2087,2183,2294,2396,2506,2665,2762,6645"}}]}]}