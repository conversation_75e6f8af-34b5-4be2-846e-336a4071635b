// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supporter_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SupporterModelAdapter extends TypeAdapter<SupporterModel> {
  @override
  final int typeId = 0;

  @override
  SupporterModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SupporterModel(
      name: fields[0] as String,
      phone: fields[1] as String,
      lastUpdated: fields[2] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SupporterModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.phone)
      ..writeByte(2)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SupporterModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
