import 'package:hive/hive.dart';

part 'supporter_model.g.dart';

@HiveType(typeId: 0)
class SupporterModel extends HiveObject {
  @HiveField(0)
  String name;

  @HiveField(1)
  String phone;

  @HiveField(2)
  DateTime lastUpdated;

  SupporterModel({
    required this.name,
    required this.phone,
    required this.lastUpdated,
  });

  // Factory constructor to create from JSON
  factory SupporterModel.fromJson(Map<String, dynamic> json) {
    return SupporterModel(
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      lastUpdated: DateTime.now(),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  // Override equality operator for comparison
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SupporterModel &&
        other.name == name &&
        other.phone == phone;
  }

  @override
  int get hashCode => name.hashCode ^ phone.hashCode;

  @override
  String toString() {
    return 'SupporterModel(name: $name, phone: $phone, lastUpdated: $lastUpdated)';
  }
}
