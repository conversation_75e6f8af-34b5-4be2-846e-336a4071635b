import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../models/supporter_model.dart';
import 'local_database_service.dart';

class SupportersSyncService {
  static const String _apiEndpoint = 'https://followsafe.runasp.net/Offline/Supporters-Phones';
  
  final LocalDatabaseService _localDb;
  
  SupportersSyncService(this._localDb);

  // Fetch supporters from API
  Future<List<SupporterModel>?> fetchSupportersFromAPI() async {
    try {
      log('🌐 Fetching supporters from API: $_apiEndpoint');
      
      final response = await http.get(
        Uri.parse(_apiEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        
        final supporters = jsonData.map((json) => SupporterModel.fromJson(json)).toList();
        
        log('✅ Successfully fetched ${supporters.length} supporters from API');
        return supporters;
      } else {
        log('❌ API request failed with status code: ${response.statusCode}');
        log('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      log('❌ Error fetching supporters from API: $e');
      return null;
    }
  }

  // Sync supporters data
  Future<bool> syncSupporters({bool forceSync = false}) async {
    try {
      log('🔄 Starting supporters sync process...');
      
      // Check if sync is needed
      if (!forceSync && !_localDb.isSyncNeeded()) {
        log('⏭️ Sync not needed yet (less than 48 hours since last sync)');
        return true;
      }

      // Fetch new data from API
      final newSupporters = await fetchSupportersFromAPI();
      if (newSupporters == null) {
        log('❌ Failed to fetch supporters from API');
        return false;
      }

      // Get current local data
      final currentSupporters = _localDb.getAllSupporters();
      
      // Compare data
      if (_localDb.areSupporersEqual(currentSupporters, newSupporters)) {
        log('✅ No changes detected in supporters data');
        // Update last sync time even if no changes
        await _localDb.saveSupporters(currentSupporters);
        return true;
      }

      // Data is different, update local database
      log('🔄 Supporters data changed, updating local database...');
      log('Previous count: ${currentSupporters.length}, New count: ${newSupporters.length}');
      
      final success = await _localDb.saveSupporters(newSupporters);
      
      if (success) {
        log('✅ Successfully synced ${newSupporters.length} supporters');
        return true;
      } else {
        log('❌ Failed to save supporters to local database');
        return false;
      }
    } catch (e) {
      log('❌ Error during sync process: $e');
      return false;
    }
  }

  // Get supporters count from local database
  int getLocalSupportersCount() {
    return _localDb.getSupporterCount();
  }

  // Get all supporters from local database
  List<SupporterModel> getLocalSupporters() {
    return _localDb.getAllSupporters();
  }

  // Get last sync time
  DateTime? getLastSyncTime() {
    return _localDb.getLastSyncTime();
  }

  // Check if initial sync is needed
  bool isInitialSyncNeeded() {
    return _localDb.getSupporterCount() == 0 || _localDb.getLastSyncTime() == null;
  }

  // Perform initial sync
  Future<bool> performInitialSync() async {
    log('🚀 Performing initial supporters sync...');
    return await syncSupporters(forceSync: true);
  }

  // Get sync status info
  Map<String, dynamic> getSyncStatus() {
    final lastSync = getLastSyncTime();
    final supportersCount = getLocalSupportersCount();
    final syncNeeded = _localDb.isSyncNeeded();
    
    return {
      'lastSync': lastSync?.toIso8601String(),
      'supportersCount': supportersCount,
      'syncNeeded': syncNeeded,
      'hoursSinceLastSync': lastSync != null 
          ? DateTime.now().difference(lastSync).inHours 
          : null,
    };
  }
}
