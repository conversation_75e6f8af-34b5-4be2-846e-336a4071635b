import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:developer';
import '../services/connectivity_service.dart';
import '../services/sms_service.dart';
import '../services/local_database_service.dart';
import '../services/supporters_sync_service.dart';
import '../cubit/sos/sos_cubit.dart';
import '../cubit/sos/sos_state.dart';
import '../models/supporter_model.dart';

class SosDebugPage extends StatefulWidget {
  @override
  _SosDebugPageState createState() => _SosDebugPageState();
}

class _SosDebugPageState extends State<SosDebugPage> {
  final ConnectivityService _connectivityService = ConnectivityService();
  final SmsService _smsService = SmsService();
  final LocalDatabaseService _localDb = LocalDatabaseService();
  late SupportersSyncService _syncService;

  String _connectionStatus = 'Unknown';
  String _smsStatus = 'Unknown';
  String _dbStatus = 'Unknown';
  int _supportersCount = 0;
  String _lastSync = 'Never';

  @override
  void initState() {
    super.initState();
    _syncService = SupportersSyncService(_localDb);
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      await _connectivityService.initialize();
      await _localDb.initialize();
      _updateStatus();
    } catch (e) {
      log('Error initializing services: $e');
    }
  }

  Future<void> _updateStatus() async {
    try {
      // Check connectivity
      final isOnline = await _connectivityService.hasInternetConnection();
      final connectionType = await _connectivityService.getConnectivityType();
      
      // Check SMS
      final canSendSMS = await _smsService.canSendSMS();
      final smsStatusMap = await _smsService.getSMSStatus();
      
      // Check database
      final supporters = _localDb.getAllSupporters();
      final lastSyncTime = _localDb.getLastSyncTime();
      
      setState(() {
        _connectionStatus = isOnline ? 'Online ($connectionType)' : 'Offline';
        _smsStatus = canSendSMS ? 'Ready' : 'Not Ready';
        _supportersCount = supporters.length;
        _lastSync = lastSyncTime?.toString() ?? 'Never';
        _dbStatus = 'Initialized';
      });
      
      log('Status updated: Connection=$_connectionStatus, SMS=$_smsStatus, Supporters=$_supportersCount');
    } catch (e) {
      log('Error updating status: $e');
      setState(() {
        _connectionStatus = 'Error';
        _smsStatus = 'Error';
        _dbStatus = 'Error';
      });
    }
  }

  Future<void> _testSMS() async {
    try {
      // Add test supporter if none exist
      if (_supportersCount == 0) {
        final testSupporter = SupporterModel(
          name: 'Test Supporter',
          phone: '+966501234567', // Replace with actual test number
          lastUpdated: DateTime.now(),
        );
        await _localDb.saveSupporters([testSupporter]);
        _updateStatus();
      }
      
      final supporters = _localDb.getAllSupporters();
      if (supporters.isNotEmpty) {
        final success = await _smsService.sendEmergencySMS(supporters);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'SMS Test Successful' : 'SMS Test Failed'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      log('Error testing SMS: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('SMS Test Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _testSync() async {
    try {
      final success = await _syncService.syncSupporters(forceSync: true);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Sync Successful' : 'Sync Failed'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
      _updateStatus();
    } catch (e) {
      log('Error testing sync: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Sync Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('SOS Debug Page'),
        backgroundColor: Colors.red,
      ),
      body: BlocListener<SosCubit, SosState>(
        listener: (context, state) {
          if (state is SosSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('SOS Sent Successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is SosFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('SOS Failed: ${state.error}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('System Status', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      SizedBox(height: 8),
                      Text('Connection: $_connectionStatus'),
                      Text('SMS: $_smsStatus'),
                      Text('Database: $_dbStatus'),
                      Text('Supporters Count: $_supportersCount'),
                      Text('Last Sync: $_lastSync'),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _updateStatus,
                      child: Text('Refresh Status'),
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _testSync,
                      child: Text('Test Sync'),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _testSMS,
                      child: Text('Test SMS'),
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: BlocBuilder<SosCubit, SosState>(
                      builder: (context, state) {
                        return ElevatedButton(
                          onPressed: state is SosLoading ? null : () {
                            context.read<SosCubit>().sendSosNotification(
                              message: "Test SOS from Debug Page",
                            );
                          },
                          child: state is SosLoading 
                              ? CircularProgressIndicator(color: Colors.white)
                              : Text('Test SOS'),
                          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                        );
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Supporters List', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                        SizedBox(height: 8),
                        Expanded(
                          child: ListView.builder(
                            itemCount: _supportersCount,
                            itemBuilder: (context, index) {
                              final supporters = _localDb.getAllSupporters();
                              if (index < supporters.length) {
                                final supporter = supporters[index];
                                return ListTile(
                                  title: Text(supporter.name),
                                  subtitle: Text(supporter.phone),
                                  trailing: Text(supporter.lastUpdated.toString().substring(0, 16)),
                                );
                              }
                              return SizedBox.shrink();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _connectivityService.dispose();
    super.dispose();
  }
}
