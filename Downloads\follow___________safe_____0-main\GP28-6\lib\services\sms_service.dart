import 'dart:developer';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/supporter_model.dart';

class SmsService {
  static final SmsService _instance = SmsService._internal();
  factory SmsService() => _instance;
  SmsService._internal();

  static const MethodChannel _channel = MethodChannel('sms_sender');

  // Check if SMS permissions are granted
  Future<bool> hasPermissions() async {
    try {
      final smsPermission = await Permission.sms.status;
      log('📱 SMS permissions status: $smsPermission');
      return smsPermission.isGranted;
    } catch (e) {
      log('❌ Error checking SMS permissions: $e');
      return false;
    }
  }

  // Request SMS permissions
  Future<bool> requestPermissions() async {
    try {
      log('📱 Requesting SMS permissions...');

      final smsPermission = await Permission.sms.request();

      if (smsPermission.isGranted) {
        log('✅ SMS permissions granted');
        return true;
      } else if (smsPermission.isPermanentlyDenied) {
        log('❌ SMS permissions permanently denied');
        await openAppSettings();
        return false;
      } else {
        log('❌ SMS permissions denied');
        return false;
      }
    } catch (e) {
      log('❌ Error requesting SMS permissions: $e');
      return false;
    }
  }

  // Send SMS to a single phone number
  Future<bool> sendSingleSMS(String phoneNumber, String message) async {
    try {
      // Check permissions first
      if (!await hasPermissions()) {
        final granted = await requestPermissions();
        if (!granted) {
          log('❌ SMS permissions not granted');
          return false;
        }
      }

      log('📤 Sending SMS to $phoneNumber');

      final result = await _channel.invokeMethod('sendSMS', {
        'phoneNumber': phoneNumber,
        'message': message,
      });

      if (result == true) {
        log('✅ SMS sent successfully to $phoneNumber');
        return true;
      } else {
        log('❌ SMS failed to send to $phoneNumber');
        return false;
      }
    } catch (e) {
      log('❌ Error sending SMS to $phoneNumber: $e');
      return false;
    }
  }

  // Send SMS to multiple supporters
  Future<Map<String, bool>> sendSMSToSupporters(
      List<SupporterModel> supporters, String message) async {
    final results = <String, bool>{};

    if (supporters.isEmpty) {
      log('⚠️ No supporters to send SMS to');
      return results;
    }

    log('📤 Sending SMS to ${supporters.length} supporters...');

    // Check permissions first
    if (!await hasPermissions()) {
      final granted = await requestPermissions();
      if (!granted) {
        log('❌ SMS permissions not granted, cannot send messages');
        // Mark all as failed
        for (final supporter in supporters) {
          results[supporter.phone] = false;
        }
        return results;
      }
    }

    int successCount = 0;
    int failureCount = 0;

    for (final supporter in supporters) {
      try {
        final success = await sendSingleSMS(supporter.phone, message);
        results[supporter.phone] = success;

        if (success) {
          successCount++;
          log('✅ SMS sent to ${supporter.name} (${supporter.phone})');
        } else {
          failureCount++;
          log('❌ Failed to send SMS to ${supporter.name} (${supporter.phone})');
        }

        // Add small delay between messages to avoid overwhelming the system
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        results[supporter.phone] = false;
        failureCount++;
        log('❌ Error sending SMS to ${supporter.name} (${supporter.phone}): $e');
      }
    }

    log('📊 SMS sending completed: $successCount successful, $failureCount failed');
    return results;
  }

  // Send emergency SOS SMS to all supporters
  Future<bool> sendEmergencySMS(List<SupporterModel> supporters) async {
    const emergencyMessage = "أنا في خطر! الرجاء المساعدة";

    log('🚨 Sending emergency SMS to ${supporters.length} supporters');

    final results = await sendSMSToSupporters(supporters, emergencyMessage);

    // Count successful sends
    final successCount = results.values.where((success) => success).length;
    final totalCount = supporters.length;

    log('🚨 Emergency SMS results: $successCount/$totalCount sent successfully');

    // Return true if at least one SMS was sent successfully
    return successCount > 0;
  }

  // Check if device can send SMS
  Future<bool> canSendSMS() async {
    try {
      // Check permissions
      final hasPerms = await hasPermissions();

      log('📱 SMS capability check: permissions=$hasPerms');
      return hasPerms;
    } catch (e) {
      log('❌ Error checking SMS capability: $e');
      return false;
    }
  }

  // Get SMS sending status info
  Future<Map<String, dynamic>> getSMSStatus() async {
    try {
      final canSend = await canSendSMS();
      final hasPerms = await hasPermissions();

      return {
        'canSendSMS': canSend,
        'hasPermissions': hasPerms,
        'isSmsCapable': true, // Assume true for Android devices
      };
    } catch (e) {
      log('❌ Error getting SMS status: $e');
      return {
        'canSendSMS': false,
        'hasPermissions': false,
        'isSmsCapable': false,
        'error': e.toString(),
      };
    }
  }
}
