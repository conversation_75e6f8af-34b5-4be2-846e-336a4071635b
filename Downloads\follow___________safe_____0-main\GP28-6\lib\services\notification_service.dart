import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:pro/home/<USER>/sos_location_map.dart';
import 'package:pro/models/SosNotificationModel.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // For in-app notifications
  final List<SosNotificationModel> _notifications = [];
  final ValueNotifier<List<SosNotificationModel>> notificationsNotifier =
      ValueNotifier<List<SosNotificationModel>>([]);

  NotificationService._internal();

  Future<void> initialize() async {
    // Initialize local notifications
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );

    // Request notification permissions
    await _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  void _onNotificationTap(NotificationResponse response) {
    // Handle notification tap
    log('Notification tapped: ${response.payload}');

    // For now, we can't open the map directly from here because we don't have context
    // and we need the full notification object
    log('To view the location, please open the app and check the notifications page');
  }

  void _openMap(BuildContext context, double latitude, double longitude,
      SosNotificationModel notification) {
    // Navigate to the SOS location map screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SosLocationMapScreen(
          notification: notification,
        ),
      ),
    );
  }

  // Show a local push notification
  Future<void> showSosNotification(SosNotificationModel notification) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'sos_channel',
        'SOS Alerts',
        channelDescription: 'Notifications for SOS alerts from travelers',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      final payload =
          'latitude: ${notification.latitude}, longitude: ${notification.longitude}, message: ${notification.message}';

      await _flutterLocalNotificationsPlugin.show(
        notification.hashCode,
        'SOS Alert from ${notification.travelerName}',
        notification.message,
        platformChannelSpecifics,
        payload: payload,
      );

      // Also add to in-app notifications
      addInAppNotification(notification);

      log('SOS notification shown: ${notification.message}');
    } catch (e) {
      log('Error showing SOS notification: $e');
    }
  }

  // Show an in-app dialog for SOS notifications
  void showSosDialog(BuildContext context, SosNotificationModel notification) {
    final timeAgo = _getTimeAgo(notification.timestamp);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.warning_amber_rounded,
                  color: Colors.red, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'SOS Alert from ${notification.travelerName}!',
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.warning_amber_rounded,
                          color: Colors.red),
                      const SizedBox(width: 8),
                      Expanded(
                          child: Text(notification.message,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold))),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                // Traveler info
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xff193869).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.person,
                          size: 16, color: Color(0xff193869)),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Traveler: ${notification.travelerName}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Color(0xff193869),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                // Time info
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(timeAgo, style: const TextStyle(color: Colors.grey)),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.blue),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Latitude: ${notification.latitude.toStringAsFixed(6)}\nLongitude: ${notification.longitude.toStringAsFixed(6)}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                  'This traveler needs your immediate assistance!',
                  style:
                      TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton.icon(
              icon: const Icon(Icons.map, color: Colors.blue),
              label: const Text('View on Map',
                  style: TextStyle(color: Colors.blue)),
              onPressed: () {
                Navigator.of(context).pop();
                _openMap(context, notification.latitude, notification.longitude,
                    notification);
              },
            ),
            TextButton.icon(
              icon: const Icon(Icons.close, color: Colors.grey),
              label: const Text('Close', style: TextStyle(color: Colors.grey)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  // Add notification to in-app list
  void addInAppNotification(SosNotificationModel notification) {
    _notifications.add(notification);
    notificationsNotifier.value = List.from(_notifications);
  }

  // Clear a specific notification
  void clearNotification(SosNotificationModel notification) {
    _notifications.removeWhere((n) =>
        n.travelerId == notification.travelerId &&
        n.timestamp == notification.timestamp);
    notificationsNotifier.value = List.from(_notifications);
  }

  // Clear all notifications
  void clearAllNotifications() {
    _notifications.clear();
    notificationsNotifier.value = [];
    _flutterLocalNotificationsPlugin.cancelAll();
  }

  // Get unread notifications count
  int get unreadCount => _notifications.length;
}
