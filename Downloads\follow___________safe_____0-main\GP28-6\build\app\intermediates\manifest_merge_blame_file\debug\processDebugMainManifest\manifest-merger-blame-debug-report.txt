1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.pro"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:8:5-67
15-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:8:22-64
16    <!-- SMS permissions -->
17    <uses-permission android:name="android.permission.SEND_SMS" />
17-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:3:5-67
17-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:3:22-64
18    <uses-permission android:name="android.permission.READ_SMS" />
18-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:4:5-67
18-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:4:22-64
19    <uses-permission android:name="android.permission.RECEIVE_SMS" />
19-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:5:5-70
19-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:5:22-67
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:9:5-79
20-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:9:22-76
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:48:5-53:15
29        <intent>
29-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:49:9-52:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:50:13-72
30-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:50:21-70
31
32            <data android:mimeType="text/plain" />
32-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:51:13-50
32-->C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\AndroidManifest.xml:51:19-48
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.VIBRATE" />
36-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
36-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
37-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
38    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
38-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
38-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
39    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
39-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
39-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-76
40
41    <permission
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\713c4a2f784e4f0f5a173a73d6f96c0d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
42        android:name="com.example.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\713c4a2f784e4f0f5a173a73d6f96c0d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\713c4a2f784e4f0f5a173a73d6f96c0d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="com.example.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\713c4a2f784e4f0f5a173a73d6f96c0d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\713c4a2f784e4f0f5a173a73d6f96c0d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
46
47    <application
48        android:name="android.app.Application"
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\713c4a2f784e4f0f5a173a73d6f96c0d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="true"
52        android:icon="@mipmap/ic_launcher"
53        android:label="pro" >
54        <activity
55            android:name="com.example.pro.MainActivity"
56            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57            android:exported="true"
58            android:hardwareAccelerated="true"
59            android:launchMode="singleTop"
60            android:taskAffinity=""
61            android:theme="@style/LaunchTheme"
62            android:windowSoftInputMode="adjustResize" >
63
64            <!--
65                 Specifies an Android theme to apply to this Activity as soon as
66                 the Android process has started. This theme is visible to the user
67                 while the Flutter UI initializes. After that, this theme continues
68                 to determine the Window background behind the Flutter UI.
69            -->
70            <meta-data
71                android:name="io.flutter.embedding.android.NormalTheme"
72                android:resource="@style/NormalTheme" />
73
74            <intent-filter>
75                <action android:name="android.intent.action.MAIN" />
76
77                <category android:name="android.intent.category.LAUNCHER" />
78            </intent-filter>
79        </activity>
80        <!--
81             Don't delete the meta-data below.
82             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
83        -->
84        <meta-data
85            android:name="flutterEmbedding"
86            android:value="2" />
87
88        <provider
88-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
89            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
89-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
90            android:authorities="com.example.pro.flutter.image_provider"
90-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
91            android:exported="false"
91-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
92            android:grantUriPermissions="true" >
92-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
93            <meta-data
93-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
94                android:name="android.support.FILE_PROVIDER_PATHS"
94-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
95                android:resource="@xml/flutter_image_picker_file_paths" />
95-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
96        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
97        <service
97-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
98            android:name="com.google.android.gms.metadata.ModuleDependencies"
98-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
99            android:enabled="false"
99-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
100            android:exported="false" >
100-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
101            <intent-filter>
101-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
102                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
102-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
102-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
103            </intent-filter>
104
105            <meta-data
105-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
106                android:name="photopicker_activity:0:required"
106-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
107                android:value="" />
107-->[:image_picker_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
108        </service>
109        <service
109-->[:geolocator_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
110            android:name="com.baseflow.geolocator.GeolocatorLocationService"
110-->[:geolocator_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
111            android:enabled="true"
111-->[:geolocator_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
112            android:exported="false"
112-->[:geolocator_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
113            android:foregroundServiceType="location" />
113-->[:geolocator_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
114        <service
114-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-15:56
115            android:name="com.lyokone.location.FlutterLocationService"
115-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-71
116            android:enabled="true"
116-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-35
117            android:exported="false"
117-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
118            android:foregroundServiceType="location" />
118-->[:location] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-53
119
120        <activity
120-->[:url_launcher_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
121            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
121-->[:url_launcher_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
122            android:exported="false"
122-->[:url_launcher_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
123            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
123-->[:url_launcher_android] C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
124        <activity
124-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daa030cc929207e77a9120361871ed1\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
125            android:name="com.google.android.gms.common.api.GoogleApiActivity"
125-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daa030cc929207e77a9120361871ed1\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
126            android:exported="false"
126-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daa030cc929207e77a9120361871ed1\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
127            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
127-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8daa030cc929207e77a9120361871ed1\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
128
129        <meta-data
129-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ad2769cfe3f8f14a92393f5169ebde4\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
130            android:name="com.google.android.gms.version"
130-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ad2769cfe3f8f14a92393f5169ebde4\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
131            android:value="@integer/google_play_services_version" />
131-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3ad2769cfe3f8f14a92393f5169ebde4\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
132
133        <uses-library
133-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d465bd7624f9a5f2819cc2d8d5353fc9\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
134            android:name="androidx.window.extensions"
134-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d465bd7624f9a5f2819cc2d8d5353fc9\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
135            android:required="false" />
135-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d465bd7624f9a5f2819cc2d8d5353fc9\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
136        <uses-library
136-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d465bd7624f9a5f2819cc2d8d5353fc9\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
137            android:name="androidx.window.sidecar"
137-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d465bd7624f9a5f2819cc2d8d5353fc9\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
138            android:required="false" />
138-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d465bd7624f9a5f2819cc2d8d5353fc9\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
139
140        <provider
140-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
141            android:name="androidx.startup.InitializationProvider"
141-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
142            android:authorities="com.example.pro.androidx-startup"
142-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
143            android:exported="false" >
143-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
144            <meta-data
144-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
145                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
145-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
146                android:value="androidx.startup" />
146-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
147            <meta-data
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
148                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
149                android:value="androidx.startup" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
150        </provider>
151
152        <receiver
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
153            android:name="androidx.profileinstaller.ProfileInstallReceiver"
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
154            android:directBootAware="false"
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
155            android:enabled="true"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
156            android:exported="true"
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
157            android:permission="android.permission.DUMP" >
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
158            <intent-filter>
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
159                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
160            </intent-filter>
161            <intent-filter>
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
162                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
163            </intent-filter>
164            <intent-filter>
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
165                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
166            </intent-filter>
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
168                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
169            </intent-filter>
170        </receiver>
171    </application>
172
173</manifest>
