import 'dart:convert';
import 'dart:developer';
import 'package:dartz/dartz.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/core/API/api_consumer.dart';
import 'package:pro/core/di/di.dart';
import 'package:pro/core/errors/Exceptions.dart';
import 'package:pro/models/SosNotificationModel.dart';
import 'package:pro/repo/SupporterRepository.dart';
import 'package:pro/services/signalr_service.dart';

class SosRepository {
  final ApiConsumer api;
  final SignalRService signalRService;

  static const String sosEndpoint =
      'https://followsafe.runasp.net/notifications/traveler-sos';

  SosRepository({required this.api, required this.signalRService});

  // Get the current user ID from cache or token
  String? getUserId() {
    // Try to get user ID from different sources
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      // Try to extract from token
      final token = CacheHelper.getData(key: ApiKey.token);
      if (token != null) {
        final extractedId = _extractUserIdFromToken(token.toString());
        if (extractedId != null) {
          log("Extracted user ID from token: $extractedId");
          return extractedId;
        }
      }

      log("WARNING: Could not get user ID from cache or token");
      return null;
    }

    return userId.toString();
  }

  // Extract user ID from JWT token
  String? _extractUserIdFromToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final Map<String, dynamic> claims = jsonDecode(decoded);

      final possibleKeys = [
        'sub',
        'user_id',
        'userId',
        'id',
        'nameid',
        'unique_name'
      ];
      for (String key in possibleKeys) {
        if (claims.containsKey(key)) {
          log("Found user ID in token with key '$key': ${claims[key]}");
          return claims[key].toString();
        }
      }

      log("No user ID found in token claims: ${claims.keys.toList()}");
    } catch (e) {
      log("Error decoding JWT token: $e");
    }
    return null;
  }

  // Get the current user name from cache or token
  String? getUserName() {
    // First, try to get name from cache (including token-extracted values)
    final possibleKeys = [
      "user_name_from_token", // Prioritize token-extracted name
      "user_email_from_token", // Fallback to token-extracted email
      ApiKey.name, // "fullname"
      "fullname",
      "name",
      "username",
      "displayName",
      "firstName",
      "lastName",
      "user_name",
      "userName",
      "display_name",
      "full_name",
      ApiKey.email, // Also try email as fallback
      "email",
    ];

    String? userName;

    // Try each key and use the first non-empty value found
    for (var key in possibleKeys) {
      final value = CacheHelper.getData(key: key);
      if (value != null && value.toString().isNotEmpty) {
        userName = value.toString();
        log("DEBUG: Found user name in cache with key $key: $userName");
        break;
      }
    }

    // If no name found in cache, try to extract from JWT token
    if (userName == null || userName.isEmpty) {
      userName = _extractUserInfoFromToken();
    }

    // If still no name found, use a more descriptive default
    if (userName == null || userName.isEmpty) {
      userName = "Anonymous Traveler";
      log("WARNING: Could not find user name in cache or token. Using default: $userName");
    }

    return userName;
  }

  // Extract user information from JWT token
  String? _extractUserInfoFromToken() {
    try {
      final token = CacheHelper.getData(key: ApiKey.token);
      if (token == null || token.toString().isEmpty) {
        log("DEBUG: No token found in cache");
        return null;
      }

      log("DEBUG: Attempting to decode token for user info");
      final decodedToken = JwtDecoder.decode(token.toString());
      log("DEBUG: Decoded token: $decodedToken");

      // Try to extract name from various token claims
      final possibleNameClaims = [
        'name',
        'fullname',
        'given_name',
        'family_name',
        'preferred_username',
        'username',
        'unique_name',
        'display_name',
      ];

      String? extractedName;
      for (var claim in possibleNameClaims) {
        if (decodedToken.containsKey(claim) &&
            decodedToken[claim] != null &&
            decodedToken[claim].toString().isNotEmpty) {
          extractedName = decodedToken[claim].toString();
          log("DEBUG: Found name in token claim '$claim': $extractedName");

          // Save to cache for future use
          CacheHelper.saveData(
              key: "user_name_from_token", value: extractedName);
          break;
        }
      }

      // If no name found, try to get email as fallback
      if (extractedName == null || extractedName.isEmpty) {
        final possibleEmailClaims = [
          'email',
          'email_address',
          'mail',
          'upn', // User Principal Name
        ];

        for (var claim in possibleEmailClaims) {
          if (decodedToken.containsKey(claim) &&
              decodedToken[claim] != null &&
              decodedToken[claim].toString().isNotEmpty) {
            extractedName = decodedToken[claim].toString();
            log("DEBUG: Found email in token claim '$claim': $extractedName");

            // Save to cache for future use
            CacheHelper.saveData(
                key: "user_email_from_token", value: extractedName);
            break;
          }
        }
      }

      return extractedName;
    } catch (e) {
      log("ERROR: Failed to extract user info from token: $e");
      return null;
    }
  }

  // Connect to SignalR hub
  Future<void> connectToSignalR() async {
    try {
      await signalRService.startConnection();
    } catch (e) {
      log("Error connecting to SignalR hub: $e");
      throw Exception("Failed to connect to notification service: $e");
    }
  }

  // Send SOS notification
  Future<Either<String, SosResponse>> sendSosNotification({
    required double latitude,
    required double longitude,
    String message = "SOS! I need help!",
  }) async {
    try {
      // Log token for debugging
      final token = CacheHelper.getData(key: ApiKey.token);
      log("DEBUG: Token from cache: ${token != null ? 'Found' : 'Not found'}");

      final userId = getUserId();
      if (userId == null) {
        log("ERROR: User ID not found in cache");
        return const Left("User ID not found. Please log in again.");
      }

      final userName = getUserName();
      log("DEBUG: User ID: $userId, User Name: $userName");

      // Ensure SignalR connection is established
      if (!signalRService.isConnected) {
        try {
          log("DEBUG: SignalR not connected, attempting to connect...");
          await connectToSignalR();
          log("DEBUG: SignalR connection established");
        } catch (e) {
          log("WARNING: Could not connect to real-time service: $e");
          // Continue anyway to try the HTTP request
        }
      } else {
        log("DEBUG: SignalR already connected");
      }

      // Get the supporter list for this traveler
      List<String> supporterIds = [];
      try {
        // Get SupporterRepository from DI
        final supporterRepository = getIt<SupporterRepository>();
        final supportersListModel =
            await supporterRepository.getSupportersList();

        if (supportersListModel.success &&
            supportersListModel.supporters.isNotEmpty) {
          supporterIds = supportersListModel.supporters
              .map((supporter) => supporter.id)
              .toList();
          log("DEBUG: Found ${supporterIds.length} supporters for traveler $userId");
        } else {
          log("WARNING: No supporters found for traveler $userId");
        }
      } catch (e) {
        log("ERROR: Failed to get supporters list: $e");
        // Continue with empty supporters list
      }

      // Create the SOS notification data
      final sosData = {
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'message': message,
        'travelerId': userId,
        'travelerName': userName,
        'timestamp': DateTime.now().toIso8601String(),
        'sendToSupportersOnly':
            true, // Send only to supporters in the traveler's list
        'supporterIds': supporterIds, // Include the list of supporter IDs
      };

      log("DEBUG: Sending SOS notification to endpoint: $sosEndpoint");
      log("DEBUG: SOS notification data: $sosData");

      try {
        // Send the SOS notification via HTTP POST
        final response = await api.post(
          sosEndpoint,
          data: sosData,
        );

        log("DEBUG: SOS notification response: $response");
        log("DEBUG: SOS notification response type: ${response.runtimeType}");

        if (response is Map<String, dynamic>) {
          log("DEBUG: Response details: ${response.keys.join(', ')}");
          response.forEach((key, value) {
            log("DEBUG: Response $key: $value");
          });
        } else if (response is List) {
          log("DEBUG: Response is a List with ${response.length} items");
        } else {
          log("DEBUG: Response is of type ${response.runtimeType}");
        }

        return Right(SosResponse.fromJson(response is Map<String, dynamic>
            ? response
            : {'success': true, 'message': 'SOS notification sent'}));
      } catch (e) {
        log("ERROR: Failed to send SOS notification via HTTP: $e");
        return Left("Failed to send SOS notification: $e");
      }
    } on ServerException catch (e) {
      log("ERROR: Server exception: ${e.errModel.description}");
      return Left(e.errModel.description);
    } catch (e) {
      log("ERROR: Unhandled exception sending SOS notification: $e");
      return Left("Failed to send SOS notification: $e");
    }
  }

  // Disconnect from SignalR hub
  Future<void> disconnectFromSignalR() async {
    await signalRService.stopConnection();
  }

  // Dispose resources
  void dispose() {
    signalRService.dispose();
  }
}
