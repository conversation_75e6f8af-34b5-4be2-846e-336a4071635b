{"logs": [{"outputFile": "com.example.pro.app-mergeDebugResources-43:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ae188e1632e2151ea3440bd966c549e8\\transformed\\preference-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,749", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "170,267,345,487,656,744,826"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5882,6061,6481,6559,6884,7053,7141", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "5947,6153,6554,6696,7048,7136,7218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8daa030cc929207e77a9120361871ed1\\transformed\\jetified-play-services-base-18.3.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3553,3661,3827,3959,4067,4228,4359,4482,4734,4905,5014,5184,5317,5494,5672,5742,5804", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "3656,3822,3954,4062,4223,4354,4477,4583,4900,5009,5179,5312,5489,5667,5737,5799,5877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3ad2769cfe3f8f14a92393f5169ebde4\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4588", "endColumns": "145", "endOffsets": "4729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\581188cde9253f2ea651785e94e07b23\\transformed\\appcompat-1.1.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,911,1002,1094,1189,1283,1383,1476,1575,1671,1762,1853,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,80,90,91,94,93,99,92,98,95,90,90,80,106,98,98,107,107,106,158,99,81", "endOffsets": "220,329,437,522,624,740,825,906,997,1089,1184,1278,1378,1471,1570,1666,1757,1848,1929,2036,2135,2234,2342,2450,2557,2716,2816,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,911,1002,1094,1189,1283,1383,1476,1575,1671,1762,1853,1934,2041,2140,2239,2347,2455,2562,2721,6701", "endColumns": "119,108,107,84,101,115,84,80,90,91,94,93,99,92,98,95,90,90,80,106,98,98,107,107,106,158,99,81", "endOffsets": "220,329,437,522,624,740,825,906,997,1089,1184,1278,1378,1471,1570,1666,1757,1848,1929,2036,2135,2234,2342,2450,2557,2716,2816,6778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13ca1886765b845003c294694287a150\\transformed\\browser-1.8.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5952,6158,6260,6375", "endColumns": "108,101,114,105", "endOffsets": "6056,6255,6370,6476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\713c4a2f784e4f0f5a173a73d6f96c0d\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2821,2920,3022,3122,3220,3327,3433,6783", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "2915,3017,3117,3215,3322,3428,3548,6879"}}]}]}