import 'package:equatable/equatable.dart';
import 'package:pro/models/kid_profile_model.dart';

abstract class Kid<PERSON><PERSON><PERSON>leState extends Equatable {
  const KidProfileState();

  @override
  List<Object?> get props => [];
}

class KidProfileInitial extends KidP<PERSON><PERSON><PERSON>State {}

class KidPro<PERSON>leLoading extends KidProfileState {}

class KidProfileLoaded extends KidProfileState {
  final KidProfileModel profile;

  const KidProfileLoaded(this.profile);

  @override
  List<Object?> get props => [profile];
}

class KidProfileError extends KidProfileState {
  final String message;

  const KidProfileError(this.message);

  @override
  List<Object?> get props => [message];
}
