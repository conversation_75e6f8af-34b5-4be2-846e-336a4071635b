import 'dart:async';
import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:location/location.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/cubit/sos/sos_state.dart';
import 'package:pro/models/SosNotificationModel.dart';
import 'package:pro/repo/SosRepository.dart';
import 'package:pro/services/notification_service.dart';
import 'package:pro/services/connectivity_service.dart';
import 'package:pro/services/sms_service.dart';
import 'package:pro/services/local_database_service.dart';

class SosCubit extends Cubit<SosState> {
  final SosRepository sosRepository;
  StreamSubscription? _sosNotificationSubscription;
  final Location _location = Location();
  final NotificationService _notificationService = NotificationService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final SmsService _smsService = SmsService();
  final LocalDatabaseService _localDb = LocalDatabaseService();
  BuildContext? _context;

  SosCubit({required this.sosRepository}) : super(SosInitial()) {
    // Listen for incoming SOS notifications
    _sosNotificationSubscription =
        sosRepository.signalRService.sosNotifications.listen((notification) {
      // Check if this notification should be shown to this user
      if (shouldShowNotification(notification)) {
        // Emit state
        emit(SosNotificationReceived(notification));

        // Show notification
        _notificationService.showSosNotification(notification);

        // Show dialog if context is available
        if (_context != null) {
          _notificationService.showSosDialog(_context!, notification);
        }

        log('Received SOS notification from ${notification.travelerName}');
      } else {
        log('Ignoring SOS notification: User is not a supporter or the traveler');
      }
    }, onError: (error) {
      log('Error in SOS notification stream: $error');
    });
  }

  // Set context for showing dialogs
  void setContext(BuildContext context) {
    _context = context;
  }

  // Check if this notification should be shown to this user
  bool shouldShowNotification(SosNotificationModel notification) {
    // Get the current user ID
    final currentUserId =
        CacheHelper.getData(key: ApiKey.userId)?.toString() ?? '';
    if (currentUserId.isEmpty) {
      log('WARNING: Current user ID not found in cache');
      return false;
    }

    // Always show notification to the traveler who sent it
    if (notification.travelerId == currentUserId) {
      log('Showing notification to traveler (self)');
      return true;
    }

    // Check if this user is in the supporters list
    final List<String> supporterIds = notification.supporterIds ?? [];
    final bool isSupporter = supporterIds.contains(currentUserId);

    if (isSupporter) {
      log('Showing notification to supporter: $currentUserId');
      return true;
    }

    log('Not showing notification to user: $currentUserId (not traveler or supporter)');
    return false;
  }

  // Initialize location service and SignalR connection
  Future<void> initialize() async {
    try {
      // Initialize connectivity service
      await _connectivityService.initialize();

      // Initialize local database
      await _localDb.initialize();

      // Connect to SignalR hub
      await sosRepository.connectToSignalR();
      emit(const SosConnectionState(true));
    } catch (e) {
      log('Error initializing SOS service: $e');
      emit(const SosConnectionState(false));
    }
  }

  // Send SOS notification with current location
  Future<void> sendSosNotification(
      {String message = "SOS! I need help!"}) async {
    emit(SosLoading());

    try {
      // Check and request location permissions
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          emit(const SosFailure('Location services are disabled'));
          return;
        }
      }

      PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          emit(const SosFailure('Location permission denied'));
          return;
        }
      }

      // Get current location
      final locationData = await _location.getLocation();

      if (locationData.latitude == null || locationData.longitude == null) {
        emit(const SosFailure('Could not get current location'));
        return;
      }

      // Check internet connectivity
      final isOnline = await _connectivityService.hasInternetConnection();

      if (isOnline) {
        // Online mode: Send via API (original behavior)
        log('🌐 Device is online, sending SOS via API...');
        await _sendOriginalSOS(
            locationData.latitude!, locationData.longitude!, message);
      } else {
        // Offline mode: Send SMS to supporters
        log('📱 Device is offline, sending SOS via SMS...');
        await _sendOfflineSOS(
            locationData.latitude!, locationData.longitude!, message);
      }
    } catch (e) {
      log('Error sending SOS notification: $e');
      emit(SosFailure('Failed to send SOS notification: $e'));
    }
  }

  // Send SOS using original method (API + SignalR)
  Future<void> _sendOriginalSOS(
      double latitude, double longitude, String message) async {
    try {
      log('🌐 Sending SOS via original method (API + SignalR)...');

      // Send SOS notification via API (this will also trigger SignalR notifications to supporters)
      final result = await sosRepository.sendSosNotification(
        latitude: latitude,
        longitude: longitude,
        message: message,
      );

      result.fold(
        (error) => emit(SosFailure(error)),
        (response) {
          // Create a notification for the traveler who sent the SOS
          final sosNotification = SosNotificationModel(
            latitude: latitude,
            longitude: longitude,
            message: message,
            travelerId: sosRepository.getUserId() ?? '',
            travelerName: sosRepository.getUserName() ?? 'You',
            timestamp: DateTime.now(),
            // Include the same supporter IDs that were sent to the server
            supporterIds: result
                .getOrElse(() => SosResponse(success: false, message: ''))
                .supporterIds,
          );

          // Add notification to the traveler's notification list
          _notificationService.addInAppNotification(sosNotification);

          // Emit success state
          emit(SosSuccess(response));
          log('✅ SOS sent successfully via API and SignalR');
        },
      );
    } catch (e) {
      log('❌ Error sending SOS via original method: $e');
      emit(SosFailure('Failed to send SOS via original method: $e'));
    }
  }

  // Send SOS offline via SMS
  Future<void> _sendOfflineSOS(
      double latitude, double longitude, String message) async {
    try {
      log('📱 Device is offline, sending SOS via SMS...');

      // Print local database contents when offline
      log('📊 Printing local database contents for offline SOS:');
      _localDb.printDatabaseContents();

      // Get supporters from local database
      final supporters = _localDb.getAllSupporters();

      if (supporters.isEmpty) {
        log('❌ No supporters found in local database');
        emit(const SosFailure(
            'No supporters available for offline SOS. Please connect to internet to sync supporters data.'));
        return;
      }

      log('📤 Sending SMS to ${supporters.length} supporters...');

      // Send emergency SMS to all supporters
      final success = await _smsService.sendEmergencySMS(supporters);

      if (success) {
        // Create a mock response for offline mode
        final response = SosResponse(
          success: true,
          message:
              'Emergency SMS sent to ${supporters.length} supporters (Offline mode)',
          supporterIds: supporters.map((s) => s.phone).toList(),
        );

        // Create a notification for the traveler
        final sosNotification = SosNotificationModel(
          latitude: latitude,
          longitude: longitude,
          message: "أنا في خطر! الرجاء المساعدة (SMS sent)",
          travelerId: sosRepository.getUserId() ?? '',
          travelerName: sosRepository.getUserName() ?? 'You',
          timestamp: DateTime.now(),
          supporterIds: supporters.map((s) => s.phone).toList(),
        );

        // Add notification to the traveler's notification list
        _notificationService.addInAppNotification(sosNotification);

        emit(SosSuccess(response));
        log('✅ Emergency SMS sent successfully to ${supporters.length} supporters');
      } else {
        emit(const SosFailure(
            'Failed to send emergency SMS. Please check SMS permissions.'));
      }
    } catch (e) {
      log('❌ Error sending offline SOS: $e');
      emit(SosFailure('Failed to send offline SOS: $e'));
    }
  }

  @override
  Future<void> close() {
    _sosNotificationSubscription?.cancel();
    _connectivityService.dispose();
    sosRepository.dispose();
    return super.close();
  }
}
