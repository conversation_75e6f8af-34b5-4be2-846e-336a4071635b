# نظام SOS الجديد مع الدعم Offline

## الميزات الجديدة

### 1. مزامنة بيانات الداعمين تلقائياً
- يتم جلب بيانات الداعمين من الـ API كل 48 ساعة تلقائياً
- يتم تخزين البيانات محلياً باستخدام Hive
- مقارنة البيانات الجديدة مع المحفوظة محلياً وتحديثها عند الحاجة

### 2. إرسال SOS في الوضع Offline
- عند عدم توفر الإنترنت، يتم إرسال رسائل SMS تلقائياً لجميع الداعمين
- الرسالة: "أنا في خطر! الرجاء المساعدة"
- يتم استخدام الأرقام المحفوظة محلياً

### 3. فحص الاتصال التلقائي
- فحص مستمر لحالة الاتصال بالإنترنت
- تبديل تلقائي بين الوضع Online والـ Offline

## الملفات الجديدة

### Models
- `lib/models/supporter_model.dart` - نموذج بيانات الداعمين

### Services
- `lib/services/local_database_service.dart` - إدارة قاعدة البيانات المحلية
- `lib/services/supporters_sync_service.dart` - مزامنة بيانات الداعمين
- `lib/services/connectivity_service.dart` - فحص حالة الاتصال
- `lib/services/sms_service.dart` - إرسال رسائل SMS

### التحديثات
- `lib/cubit/sos/sos_cubit.dart` - تحديث لدعم الوضع Offline
- `lib/main.dart` - تهيئة النظام الجديد
- `pubspec.yaml` - إضافة المكتبات المطلوبة
- `android/app/src/main/AndroidManifest.xml` - إضافة صلاحيات SMS

## كيفية العمل

### عند تشغيل التطبيق
1. تهيئة قاعدة البيانات المحلية (Hive)
2. تهيئة خدمة فحص الاتصال
3. مزامنة أولية لبيانات الداعمين (إذا لزم الأمر)
4. بدء المؤقت للمزامنة التلقائية كل 48 ساعة

### عند الضغط على زر SOS
1. فحص حالة الاتصال بالإنترنت
2. **إذا كان متصل**: إرسال عبر API كالمعتاد
3. **إذا كان غير متصل**: 
   - جلب قائمة الداعمين من قاعدة البيانات المحلية
   - إرسال رسائل SMS لجميع الداعمين
   - عرض رسالة نجاح أو فشل

## الصلاحيات المطلوبة

### Android Manifest
```xml
<uses-permission android:name="android.permission.SEND_SMS" />
<uses-permission android:name="android.permission.READ_SMS" />
<uses-permission android:name="android.permission.RECEIVE_SMS" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### Runtime Permissions
- يتم طلب صلاحية SMS عند الحاجة
- إذا تم رفض الصلاحية، يتم توجيه المستخدم لإعدادات التطبيق

## المكتبات المستخدمة

```yaml
dependencies:
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  telephony: ^0.2.0
  connectivity_plus: ^6.0.5
  permission_handler: ^11.3.1

dev_dependencies:
  hive_generator: ^2.0.1
  build_runner: ^2.4.13
```

## API Endpoint

```
GET https://followsafe.runasp.net/Offline/Supporters-Phones
```

**Response Format:**
```json
[
  {
    "name": "اسم الداعم",
    "phone": "رقم الهاتف"
  }
]
```

## الاختبار

### اختبار الوضع Online
1. تأكد من وجود اتصال إنترنت
2. اضغط على زر SOS
3. يجب أن يتم الإرسال عبر API

### اختبار الوضع Offline
1. قم بإيقاف الإنترنت أو الـ WiFi
2. اضغط على زر SOS
3. يجب أن يتم إرسال رسائل SMS

### اختبار المزامنة
1. تحقق من logs التطبيق لرؤية عملية المزامنة
2. يمكن فرض المزامنة عبر إعادة تشغيل التطبيق

## ملاحظات مهمة

1. **البيانات المحلية**: يتم حفظ بيانات الداعمين محلياً لضمان عمل النظام حتى بدون إنترنت
2. **المزامنة التلقائية**: تحدث كل 48 ساعة تلقائياً
3. **رسائل SMS**: تُرسل باللغة العربية: "أنا في خطر! الرجاء المساعدة"
4. **الصلاحيات**: يجب الموافقة على صلاحيات SMS لعمل النظام في الوضع Offline
5. **التكلفة**: إرسال رسائل SMS قد يكون له تكلفة حسب خطة المستخدم

## استكشاف الأخطاء

### لا يتم إرسال SMS
- تحقق من صلاحيات SMS
- تأكد من وجود أرقام داعمين في قاعدة البيانات المحلية
- تحقق من رصيد الرسائل في الهاتف

### لا تتم المزامنة
- تحقق من الاتصال بالإنترنت
- تحقق من صحة API endpoint
- راجع logs التطبيق للأخطاء

### مشاكل قاعدة البيانات المحلية
- امسح بيانات التطبيق وأعد تشغيله
- تحقق من صلاحيات التخزين
