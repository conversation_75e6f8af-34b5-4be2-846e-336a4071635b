com.example.pro.app-fragment-1.7.1-0 C:\Users\<USER>\.gradle\caches\transforms-3\0006d3b6746f8162d799796c88b5c16e\transformed\fragment-1.7.1\res
com.example.pro.app-jetified-window-java-1.2.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\01a8d642fcf42f1f9751278568a97906\transformed\jetified-window-java-1.2.0\res
com.example.pro.app-coordinatorlayout-1.0.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\02a56d97c3526b36963d24eeb6bcb09c\transformed\coordinatorlayout-1.0.0\res
com.example.pro.app-jetified-activity-1.9.3-3 C:\Users\<USER>\.gradle\caches\transforms-3\0efc8aa1fa47bc43a2b1184c319481b1\transformed\jetified-activity-1.9.3\res
com.example.pro.app-browser-1.8.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\13ca1886765b845003c294694287a150\transformed\browser-1.8.0\res
com.example.pro.app-jetified-annotation-experimental-1.4.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\356df10a288b86096b4c627914f498d1\transformed\jetified-annotation-experimental-1.4.0\res
com.example.pro.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\35faffe41cdcf6431d288a819cb747a5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.pro.app-jetified-play-services-basement-18.3.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\3ad2769cfe3f8f14a92393f5169ebde4\transformed\jetified-play-services-basement-18.3.0\res
com.example.pro.app-transition-1.4.1-8 C:\Users\<USER>\.gradle\caches\transforms-3\40301ff9921a96dc0a881b25b80bb74b\transformed\transition-1.4.1\res
com.example.pro.app-jetified-tracing-1.2.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\463134e56b65922896e37219e6734b35\transformed\jetified-tracing-1.2.0\res
com.example.pro.app-media-1.1.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\4c40e876dff501b86b2a6255304c5ab6\transformed\media-1.1.0\res
com.example.pro.app-appcompat-1.1.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\581188cde9253f2ea651785e94e07b23\transformed\appcompat-1.1.0\res
com.example.pro.app-core-1.13.1-12 C:\Users\<USER>\.gradle\caches\transforms-3\713c4a2f784e4f0f5a173a73d6f96c0d\transformed\core-1.13.1\res
com.example.pro.app-jetified-lifecycle-process-2.7.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\775d67d116396ba1493d70e44a2b35d9\transformed\jetified-lifecycle-process-2.7.0\res
com.example.pro.app-jetified-fragment-ktx-1.7.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\82720d3d72cef139e98274e07ab65a19\transformed\jetified-fragment-ktx-1.7.1\res
com.example.pro.app-lifecycle-viewmodel-2.7.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\8667d3e81612a5cf442a0d22eca35926\transformed\lifecycle-viewmodel-2.7.0\res
com.example.pro.app-jetified-play-services-base-18.3.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\8daa030cc929207e77a9120361871ed1\transformed\jetified-play-services-base-18.3.0\res
com.example.pro.app-jetified-activity-ktx-1.9.3-17 C:\Users\<USER>\.gradle\caches\transforms-3\8dcace58523484bae95e2d848008f3c6\transformed\jetified-activity-ktx-1.9.3\res
com.example.pro.app-jetified-lifecycle-runtime-ktx-2.7.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\8fba0a8ca838dbce1be4bc38044ae9c2\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.pro.app-core-runtime-2.2.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\8fc135b83519e67869dffa3f20ea1528\transformed\core-runtime-2.2.0\res
com.example.pro.app-lifecycle-runtime-2.7.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\925711d797cdb8733e8f78b6d78f90cf\transformed\lifecycle-runtime-2.7.0\res
com.example.pro.app-jetified-datastore-core-release-21 C:\Users\<USER>\.gradle\caches\transforms-3\95d819370b176b96d4553c79dbfaf5cf\transformed\jetified-datastore-core-release\res
com.example.pro.app-jetified-startup-runtime-1.1.1-22 C:\Users\<USER>\.gradle\caches\transforms-3\97391ec373e23745ae8b039933446e6a\transformed\jetified-startup-runtime-1.1.1\res
com.example.pro.app-jetified-core-1.0.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\a1432dcb8d4cd6645e926e96f5fd992c\transformed\jetified-core-1.0.0\res
com.example.pro.app-jetified-savedstate-ktx-1.2.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\a25dd812795691abb1f738be72cbaa3a\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.pro.app-preference-1.2.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\ae188e1632e2151ea3440bd966c549e8\transformed\preference-1.2.1\res
com.example.pro.app-jetified-core-ktx-1.13.1-26 C:\Users\<USER>\.gradle\caches\transforms-3\b38bb335c5c39e13c76c7967d17d03a2\transformed\jetified-core-ktx-1.13.1\res
com.example.pro.app-jetified-savedstate-1.2.1-27 C:\Users\<USER>\.gradle\caches\transforms-3\b470304082764bfa92aaee90d0d84f05\transformed\jetified-savedstate-1.2.1\res
com.example.pro.app-recyclerview-1.0.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\b82546682b074cc1b693f284318acb28\transformed\recyclerview-1.0.0\res
com.example.pro.app-jetified-appcompat-resources-1.1.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\bf8cbd41d97dcc7059fe92136059f36a\transformed\jetified-appcompat-resources-1.1.0\res
com.example.pro.app-slidingpanelayout-1.2.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\c224bb8feef47c108898a9e4415c35b3\transformed\slidingpanelayout-1.2.0\res
com.example.pro.app-jetified-datastore-release-31 C:\Users\<USER>\.gradle\caches\transforms-3\c6a5eaa8a3b6e2a85fd14f187d9d9a67\transformed\jetified-datastore-release\res
com.example.pro.app-jetified-window-1.2.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\d465bd7624f9a5f2819cc2d8d5353fc9\transformed\jetified-window-1.2.0\res
com.example.pro.app-jetified-lifecycle-viewmodel-ktx-2.7.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\d622be66852be1da2ee664e12bb3b9a3\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.pro.app-jetified-lifecycle-livedata-core-ktx-2.7.0-34 C:\Users\<USER>\.gradle\caches\transforms-3\e07c388438196e34f4217f51d2342212\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.pro.app-lifecycle-livedata-2.7.0-35 C:\Users\<USER>\.gradle\caches\transforms-3\e6089fe090bf8d7e37869ddae431696e\transformed\lifecycle-livedata-2.7.0\res
com.example.pro.app-jetified-profileinstaller-1.3.1-36 C:\Users\<USER>\.gradle\caches\transforms-3\e8bc5e4272e7f0cc5901f77c50fcf93d\transformed\jetified-profileinstaller-1.3.1\res
com.example.pro.app-jetified-datastore-preferences-release-37 C:\Users\<USER>\.gradle\caches\transforms-3\f04b267cb760f4b69057f7c8b0f711d7\transformed\jetified-datastore-preferences-release\res
com.example.pro.app-lifecycle-livedata-core-2.7.0-38 C:\Users\<USER>\.gradle\caches\transforms-3\ff2c36b48c43cf9dcb0daa893dc6dada\transformed\lifecycle-livedata-core-2.7.0\res
com.example.pro.app-debug-39 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\debug\res
com.example.pro.app-main-40 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\android\app\src\main\res
com.example.pro.app-pngs-41 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\generated\res\pngs\debug
com.example.pro.app-resValues-42 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\generated\res\resValues\debug
com.example.pro.app-mergeDebugResources-43 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\incremental\debug\mergeDebugResources\merged.dir
com.example.pro.app-mergeDebugResources-44 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\incremental\debug\mergeDebugResources\stripped.dir
com.example.pro.app-debug-45 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.pro.app-debug-46 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-47 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\flutter_compass\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-48 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\flutter_local_notifications\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-49 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-50 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-51 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-52 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\location\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-53 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-54 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-55 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pro.app-debug-56 C:\Users\<USER>\Downloads\follow___________safe_____0-main\GP28-6\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
