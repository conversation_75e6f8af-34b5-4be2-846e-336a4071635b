import 'package:flutter_test/flutter_test.dart';
import 'package:pro/models/supporter_model.dart';
import 'package:pro/services/local_database_service.dart';
import 'package:pro/services/supporters_sync_service.dart';
import 'package:pro/services/connectivity_service.dart';
import 'package:pro/services/sms_service.dart';

void main() {
  group('Offline SOS System Tests', () {
    late LocalDatabaseService localDb;
    late SupportersSyncService syncService;
    late ConnectivityService connectivityService;
    late SmsService smsService;

    setUpAll(() async {
      // Initialize services for testing
      localDb = LocalDatabaseService();
      syncService = SupportersSyncService(localDb);
      connectivityService = ConnectivityService();
      smsService = SmsService();
    });

    group('SupporterModel Tests', () {
      test('should create SupporterModel from JSON', () {
        final json = {
          'name': 'أحمد محمد',
          'phone': '+966501234567',
        };

        final supporter = SupporterModel.fromJson(json);

        expect(supporter.name, equals('أحمد محمد'));
        expect(supporter.phone, equals('+966501234567'));
        expect(supporter.lastUpdated, isA<DateTime>());
      });

      test('should convert SupporterModel to JSON', () {
        final supporter = SupporterModel(
          name: 'سارة أحمد',
          phone: '+966507654321',
          lastUpdated: DateTime.now(),
        );

        final json = supporter.toJson();

        expect(json['name'], equals('سارة أحمد'));
        expect(json['phone'], equals('+966507654321'));
        expect(json['lastUpdated'], isA<String>());
      });

      test('should compare SupporterModel equality correctly', () {
        final supporter1 = SupporterModel(
          name: 'محمد علي',
          phone: '+966501111111',
          lastUpdated: DateTime.now(),
        );

        final supporter2 = SupporterModel(
          name: 'محمد علي',
          phone: '+966501111111',
          lastUpdated: DateTime.now().add(Duration(hours: 1)),
        );

        final supporter3 = SupporterModel(
          name: 'أحمد محمد',
          phone: '+966501111111',
          lastUpdated: DateTime.now(),
        );

        expect(supporter1, equals(supporter2)); // Same name and phone
        expect(supporter1, isNot(equals(supporter3))); // Different name
      });
    });

    group('LocalDatabaseService Tests', () {
      test('should initialize without errors', () async {
        expect(() async => await localDb.initialize(), returnsNormally);
      });

      test('should save and retrieve supporters', () async {
        await localDb.initialize();

        final supporters = [
          SupporterModel(
            name: 'داعم 1',
            phone: '+966501111111',
            lastUpdated: DateTime.now(),
          ),
          SupporterModel(
            name: 'داعم 2',
            phone: '+966502222222',
            lastUpdated: DateTime.now(),
          ),
        ];

        final saved = await localDb.saveSupporters(supporters);
        expect(saved, isTrue);

        final retrieved = localDb.getAllSupporters();
        expect(retrieved.length, equals(2));
        expect(retrieved[0].name, equals('داعم 1'));
        expect(retrieved[1].name, equals('داعم 2'));
      });

      test('should check sync timing correctly', () async {
        await localDb.initialize();

        // Initially should need sync
        expect(localDb.isSyncNeeded(), isTrue);

        // After saving data, should not need sync immediately
        await localDb.saveSupporters([]);
        expect(localDb.isSyncNeeded(), isFalse);
      });
    });

    group('ConnectivityService Tests', () {
      test('should initialize without errors', () async {
        expect(() async => await connectivityService.initialize(), returnsNormally);
      });

      test('should check internet connection', () async {
        await connectivityService.initialize();
        final hasConnection = await connectivityService.hasInternetConnection();
        expect(hasConnection, isA<bool>());
      });

      test('should get connectivity type', () async {
        await connectivityService.initialize();
        final type = await connectivityService.getConnectivityType();
        expect(type, isA<String>());
        expect(['wifi', 'mobile', 'ethernet', 'none', 'other', 'bluetooth', 'vpn'], contains(type));
      });
    });

    group('SmsService Tests', () {
      test('should check SMS capabilities', () async {
        final canSend = await smsService.canSendSMS();
        expect(canSend, isA<bool>());
      });

      test('should get SMS status', () async {
        final status = await smsService.getSMSStatus();
        expect(status, isA<Map<String, dynamic>>());
        expect(status.containsKey('canSendSMS'), isTrue);
        expect(status.containsKey('hasPermissions'), isTrue);
        expect(status.containsKey('isSmsCapable'), isTrue);
      });
    });

    group('SupportersSyncService Tests', () {
      test('should check if initial sync is needed', () {
        final isNeeded = syncService.isInitialSyncNeeded();
        expect(isNeeded, isA<bool>());
      });

      test('should get sync status', () {
        final status = syncService.getSyncStatus();
        expect(status, isA<Map<String, dynamic>>());
        expect(status.containsKey('supportersCount'), isTrue);
        expect(status.containsKey('syncNeeded'), isTrue);
      });

      test('should get local supporters count', () {
        final count = syncService.getLocalSupportersCount();
        expect(count, isA<int>());
        expect(count, greaterThanOrEqualTo(0));
      });
    });

    tearDownAll(() async {
      // Clean up after tests
      await localDb.clearAllData();
      await localDb.close();
      connectivityService.dispose();
    });
  });
}
